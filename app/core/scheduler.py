"""
Background task scheduler for Deltek synchronization and other periodic tasks
"""

import logging
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.jobstores.memory import MemoryJobStore
from sqlalchemy.orm import Session
from app.core import stripe_service
from app.core.email_service import email_service
from app.db.database import SessionLocal
from app.core.deltek_service import deltek_service
from app.models.job import Job, JobStatus
from app.schemas.job import JobCreate
from app.crud.job import create_job, get_jobs_by_client

logger = logging.getLogger(__name__)


class SchedulerService:
    """Service for managing background scheduled tasks"""
    
    def __init__(self):
        """Initialize the scheduler service"""
        self.scheduler = None
        self.is_running = False
        self._setup_scheduler()
    
    def _setup_scheduler(self):
        """Setup the APScheduler with configuration"""
        jobstores = {
            'default': MemoryJobStore()
        }
        
        executors = {
            'default': ThreadPoolExecutor(max_workers=3)
        }
        
        job_defaults = {
            'coalesce': True,  # Combine multiple pending executions into one
            'max_instances': 1,  # Only one instance of each job at a time
            'misfire_grace_time': 300  # 5 minutes grace time for missed jobs
        }
        
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='UTC'
        )
        
        logger.info("📅 Background scheduler initialized")
    
    def start(self):
        """Start the scheduler"""
        if not self.is_running:
            try:
                self.scheduler.start()
                self.is_running = True
                logger.info("🚀 Background scheduler started")

                # Add the Deltek sync job to run every hour
                self.add_deltek_sync_job()

                # Add the Stripe payment update job to run every 5 minutes

                # Run initial sync immediately on startup
                logger.info("🔄 Running initial Deltek sync on startup...")
                try:
                    self.run_deltek_sync()
                    logger.info("✅ Initial Deltek sync completed")
                except Exception as e:
                    logger.error(f"❌ Initial Deltek sync failed: {str(e)}")

            except Exception as e:
                logger.error(f"❌ Failed to start scheduler: {str(e)}")
                raise
    
    def stop(self):
        """Stop the scheduler"""
        if self.is_running:
            try:
                self.scheduler.shutdown(wait=True)
                self.is_running = False
                logger.info("🛑 Background scheduler stopped")
            except Exception as e:
                logger.error(f"❌ Failed to stop scheduler: {str(e)}")
    
    def add_deltek_sync_job(self):
        """Add the Deltek synchronization job to run every 2 minutes"""
        try:
            # Remove existing job if it exists
            try:
                self.scheduler.remove_job('deltek_sync')
            except:
                pass  # Job doesn't exist yet

            # Add new job to run every 2 minutes
            self.scheduler.add_job(
                func=self.run_deltek_sync,
                trigger=CronTrigger(minute='*/2'),  # Run every 2 minutes
                id='deltek_sync',
                name='Deltek Invoice Synchronization',
                replace_existing=True
            )

            logger.info("⏰ Deltek sync job scheduled to run every 2 minutes")
            
        except Exception as e:
            logger.error(f"❌ Failed to add Deltek sync job: {str(e)}")

    def add_stripe_payment_update_job(self):
        """Add the Stripe payment update job to run every 5 minutes"""
        try:
            # Remove existing job if it exists
            try:
                self.scheduler.remove_job('stripe_payment_update')
            except:
                pass  # Job doesn't exist yet

            # Add new job to run every 5 minutes
            self.scheduler.add_job(
                func=self.run_stripe_payment_update,
                trigger=CronTrigger(minute='*'),  # Run every 5 minutes
                id='stripe_payment_update',
                name='Stripe Payment Status Update',
                replace_existing=True
            )

            logger.info("⏰ Stripe payment update job scheduled to run every 5 minutes")

        except Exception as e:
            logger.error(f"❌ Failed to add Stripe payment update job: {str(e)}")

    def run_deltek_sync(self):
        """
        Run Deltek synchronization task
        This method will be called by the scheduler

        Returns:
            Dict containing sync results and statistics
        """
        print("🔄 SCHEDULER: Starting scheduled Deltek synchronization...")
        logger.info("🔄 Starting scheduled Deltek synchronization...")

        db: Session = None
        sync_result = {
            "success": False,
            "message": "",
            "stats": {
                "total": 0,
                "created": 0,
                "skipped": 0,
                "errors": 0
            },
            "cash_receipt_stats": {
                "total_processed": 0,
                "successful": 0,
                "failed": 0
            }
        }

        try:
            logger.info("💰 Checking for paid jobs that need cash receipts...")
            stripe_service.stripe_service.check_and_update_pending_payments(db)
            # Get database session
            db = SessionLocal()
            
            # Fetch invoices from Deltek API
            logger.info("📡 Fetching invoices from Deltek API...")
            fetch_result = deltek_service.get_ar_balance_invoices()
            
            if not fetch_result["success"]:
                error_msg = f"Failed to fetch invoices: {fetch_result['message']}"
                logger.error(f"❌ {error_msg}")
                sync_result["message"] = error_msg
                return sync_result
            
            invoices = fetch_result["invoices"]
            logger.info(f"📋 Fetched {len(invoices)} invoices from Deltek")
            
            # Process each invoice
            stats = {
                "total": len(invoices),
                "created": 0,
                "skipped": 0,
                "errors": 0
            }
            
            for invoice in invoices:
                try:
                    # Parse invoice data
                    parse_result = deltek_service.parse_invoice_data(invoice)
                    if not parse_result["success"]:
                        logger.error(f"❌ Failed to parse invoice: {parse_result['message']}")
                        stats["errors"] += 1
                        continue
                    
                    parsed_data = parse_result["parsed_data"]
                    invoice_id = parsed_data["invoice_id"]
                    
                    # Check if invoice already exists
                    existing_job = db.query(Job).filter(Job.deltek_invoice_id == invoice_id).first()
                    if existing_job:
                        logger.debug(f"⏭️ Skipping duplicate invoice {invoice_id}")
                        stats["skipped"] += 1
                        continue
                   
                    
                    project_details = deltek_service.project_details(parsed_data['project_number'])
                    client_email = project_details['project']['BillingClientEmail']
                    client_contact = project_details['project']['BillingClientContact']
                    # email_service.send_invoice_creation_email(client_email,client_contact,parsed_data['amount'],parsed_data['project_name'])

                    # Create job from invoice
                    job_data = JobCreate(
                        client_email=client_email,
                        job_name=parsed_data["project_name"] or f"Project {parsed_data['project_number']}",
                        job_description=f"Invoice: {invoice_id}",
                        client_name=parsed_data["client_name"],
                        contract_amount=str(parsed_data["amount"]),
                        job_status=JobStatus.PENDING_PAYMENT,
                        project_deadline=parsed_data["due_date"].date() if parsed_data["due_date"] else None
                    )
                    
                    # Create the job
                    new_job = create_job(db=db, job=job_data)
                    
                    # Update with Deltek-specific fields
                    new_job.deltek_invoice_id = invoice_id
                    new_job.deltek_project_number = parsed_data["project_number"]
                    new_job.deltek_billing_client_id = parsed_data["billing_client_id"]
                    new_job.deltek_original_amount = str(parsed_data["original_amount"])
                    new_job.deltek_invoice_date = parsed_data["invoice_date"]
                    new_job.is_deltek_import = True
                    new_job.deltek_last_sync = datetime.now()
                    
                    db.commit()
                    db.refresh(new_job)
                    
                    logger.info(f"✅ Created job {new_job.id} for invoice {invoice_id} - {parsed_data['client_name']}")
                    stats["created"] += 1
                    
                except Exception as e:
                    logger.error(f"❌ Error processing invoice: {str(e)}")
                    stats["errors"] += 1
                    db.rollback()
                    continue
            
            # Log summary
            logger.info("📊 Deltek Sync Summary:")
            logger.info(f"   Total invoices: {stats['total']}")
            logger.info(f"   New jobs created: {stats['created']}")
            logger.info(f"   Skipped duplicates: {stats['skipped']}")
            logger.info(f"   Errors: {stats['errors']}")

            # Update sync result with stats
            sync_result["stats"] = stats

            # Check for paid jobs and create cash receipts
            
            # cash_receipt_stats = self.process_paid_jobs_cash_receipts(db)
            # sync_result["cash_receipt_stats"] = cash_receipt_stats

            # # Mark as successful
            # sync_result["success"] = True
            # sync_result["message"] = "Deltek synchronization completed successfully"

        except Exception as e:
            error_msg = f"Unexpected error during Deltek sync: {str(e)}"
            logger.error(f"❌ {error_msg}")
            sync_result["message"] = error_msg
            if db:
                db.rollback()

        finally:
            if db:
                db.close()
            logger.info("🏁 Scheduled Deltek synchronization completed")

        return sync_result
    
    def get_job_status(self, job_id: str):
        """Get status of a scheduled job"""
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                return {
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time,
                    "trigger": str(job.trigger)
                }
            return None
        except Exception as e:
            logger.error(f"❌ Error getting job status: {str(e)}")
            return None
    
    def list_jobs(self):
        """List all scheduled jobs"""
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time,
                    "trigger": str(job.trigger)
                })
            return jobs
        except Exception as e:
            logger.error(f"❌ Error listing jobs: {str(e)}")
            return []

    def process_paid_jobs_cash_receipts(self, db: Session):
        """
        Check for paid jobs and create cash receipts in Deltek
        This runs as part of the hourly Deltek sync

        Returns:
            Dict containing cash receipt processing statistics
        """
        cash_receipt_stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "errors": []
        }

        try:
            from app.models.billing import Billing
            from app.models.job import Job
            from app.core.deltek_service import deltek_service
            from app.core.email_service import email_service

            # Find paid billings for Deltek jobs that don't have cash receipts yet
            paid_billings_needing_receipts = db.query(Billing).join(Job).filter(
                Billing.paid == True,
                Billing.is_cash_receipt_generated == False,
                Job.is_deltek_import == True,
                Job.deltek_invoice_id.isnot(None),
                Job.deltek_project_number.isnot(None)
            ).all()

            if not paid_billings_needing_receipts:
                logger.info("✅ No paid jobs requiring cash receipts found")
                return cash_receipt_stats

            logger.info(f"💰 Found {len(paid_billings_needing_receipts)} paid jobs needing cash receipts")

            # Update stats with actual count
            cash_receipt_stats["total_processed"] = len(paid_billings_needing_receipts)

            for billing in paid_billings_needing_receipts:
                try:
                    job = billing.job
                    logger.info(f"💰 Creating cash receipt for billing {billing.id} (job {job.id}: {job.job_name})")

                    # Prepare billing data for cash receipt
                    billing_data = {
                        "job_id": job.id,
                        "job_name": job.job_name,
                        "deltek_project_number": job.deltek_project_number,
                        "deltek_invoice_id": job.deltek_invoice_id,
                        "amount": float(billing.invoice_amount)
                    }

                    # Create cash receipt in Deltek
                    cash_receipt_result = deltek_service.create_cash_receipt(billing_data)
                    email_service.send_billing_confirmation_email(job.client_email,job.client_name,job.deltek_invoice_id,float(job.contract_amount),job.job_name,)
                    email_service.send_payment_notification_email(job.client_name,job.deltek_invoice_id,float(job.contract_amount),job.job_name,job.client_email)
                    if cash_receipt_result["success"]:
                        # Mark cash receipt as generated
                        billing.is_cash_receipt_generated = True
                        db.commit()

                        cash_receipt_stats["successful"] += 1
                        logger.info(f"✅ Cash receipt created successfully for billing {billing.id}")
                    else:
                        cash_receipt_stats["failed"] += 1
                        error_msg = f"Failed to create cash receipt for billing {billing.id}: {cash_receipt_result.get('message', 'Unknown error')}"
                        logger.error(f"❌ {error_msg}")
                        cash_receipt_stats["errors"].append(error_msg)

                except Exception as e:
                    cash_receipt_stats["failed"] += 1
                    error_msg = f"Error processing cash receipt for billing {billing.id}: {str(e)}"
                    logger.error(f"❌ {error_msg}")
                    cash_receipt_stats["errors"].append(error_msg)
                    db.rollback()
                    continue

            # Log summary
            logger.info("💰 Cash Receipt Processing Summary:")
            logger.info(f"   Total processed: {cash_receipt_stats['total_processed']}")
            logger.info(f"   Successful: {cash_receipt_stats['successful']}")
            logger.info(f"   Failed: {cash_receipt_stats['failed']}")

            if cash_receipt_stats["errors"]:
                logger.error(f"   Errors encountered: {len(cash_receipt_stats['errors'])}")
                for error in cash_receipt_stats["errors"][:3]:  # Log first 3 errors
                    logger.error(f"     - {error}")

            return cash_receipt_stats

        except Exception as e:
            error_msg = f"Unexpected error during cash receipt processing: {str(e)}"
            logger.error(f"❌ {error_msg}")
            cash_receipt_stats["errors"].append(error_msg)
            return cash_receipt_stats

    def run_stripe_payment_update(self):
        """
        Check and update Stripe payment status for all clients
        This runs periodically to ensure payment status is up-to-date
        """
        logger.info("💳 Starting Stripe payment status update...")

        update_stats = {
            "total_clients_checked": 0,
            "total_jobs_checked": 0,
            "payments_updated": 0,
            "jobs_completed": 0,
            "errors": 0,
            "error_details": []
        }

        try:
            # Get database session
            db = SessionLocal()

            # Get all unique client names that have jobs
            clients_query = db.query(Job.client_name).filter(Job.client_name.isnot(None)).distinct()
            client_names = [row[0] for row in clients_query.all()]

            logger.info(f"🔍 Found {len(client_names)} unique clients to check")
            update_stats["total_clients_checked"] = len(client_names)

            # Check each client's jobs for Stripe payment updates
            for client_name in client_names:
                try:
                    logger.info(f"🔍 Checking Stripe payments for client: {client_name}")

                    # Use get_jobs_by_client with Stripe update enabled
                    jobs = get_jobs_by_client(
                        db=db,
                        client_name=client_name,
                        skip=0,
                        limit=1000,  # Check all jobs for this client
                        update_stripe_status=True
                    )

                    update_stats["total_jobs_checked"] += len(jobs)

                    # Count jobs that were updated (this is implicit in the get_jobs_by_client function)
                    completed_jobs = [job for job in jobs if job.job_status == JobStatus.COMPLETED]
                    logger.info(f"   Client {client_name}: {len(jobs)} jobs checked, {len(completed_jobs)} completed")

                except Exception as e:
                    update_stats["errors"] += 1
                    error_msg = f"Error checking client {client_name}: {str(e)}"
                    update_stats["error_details"].append(error_msg)
                    logger.error(f"❌ {error_msg}")
                    continue

            # Log summary
            logger.info("💳 Stripe Payment Update Summary:")
            logger.info(f"   Clients checked: {update_stats['total_clients_checked']}")
            logger.info(f"   Jobs checked: {update_stats['total_jobs_checked']}")
            logger.info(f"   Errors: {update_stats['errors']}")

            if update_stats["errors"] > 0:
                logger.error(f"   Error details: {update_stats['error_details']}")

            logger.info("✅ Stripe payment status update completed")

        except Exception as e:
            logger.error(f"❌ Critical error in Stripe payment update: {str(e)}")
            update_stats["errors"] += 1
            update_stats["error_details"].append(str(e))

        finally:
            try:
                db.close()
            except:
                pass


# Global scheduler instance
scheduler_service = SchedulerService()
