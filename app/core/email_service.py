"""
Email service for sending notifications
"""

import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Optional, Dict, Any
from datetime import datetime
from jinja2 import Template

from app.core.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending email notifications"""
    
    def __init__(self):
        """Initialize email service with SMTP configuration"""
        self.smtp_server = settings.MAIL_SERVER
        self.smtp_port = settings.MAIL_PORT
        self.username = settings.MAIL_USERNAME
        self.password = settings.MAIL_PASSWORD
        self.from_email = settings.MAIL_FROM
        self.from_name = settings.MAIL_FROM_NAME
        self.use_tls = settings.MAIL_STARTTLS
        self.admin_email = settings.ADMIN_EMAIL
    
    def send_email(
        self,
        to_emails: List[str],
        subject: str,
        html_content: str,
        text_content: Optional[str] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        Send email using SMTP
        
        Args:
            to_emails: List of recipient email addresses
            subject: Email subject
            html_content: HTML email content
            text_content: Plain text email content (optional)
            attachments: List of attachments (optional)
            
        Returns:
            Dict containing success status and details
        """
        try:
            logger.info(f"📧 Sending email to {', '.join(to_emails)}: {subject}")
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['From'] = f"{self.from_name} <{self.from_email}>"
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = subject

            # Add text content
            if text_content:
                text_part = MIMEText(text_content, 'plain')
                msg.attach(text_part)

            # Add HTML content
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)

            # Add attachments if any
            if attachments:
                for attachment in attachments:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment['content'])
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment["filename"]}'
                    )
                    msg.attach(part)
            
            # Send email
            if self.password:  # Only send if password is configured
                with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                    if self.use_tls:
                        server.starttls()
                    server.login(self.username, self.password)
                    server.send_message(msg)
                
                logger.info(f"✅ Email sent successfully to {', '.join(to_emails)}")
                return {
                    "success": True,
                    "message": "Email sent successfully",
                    "recipients": to_emails
                }
            else:
                logger.warning("⚠️ Email not sent - MAIL_PASSWORD not configured")
                return {
                    "success": False,
                    "message": "Email not sent - MAIL_PASSWORD not configured",
                    "recipients": to_emails
                }
                
        except Exception as e:
            error_msg = f"Failed to send email: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg,
                "recipients": to_emails
            }
    
    def send_billing_confirmation_email(
        self,
        customer_email: str,
        customer_name: str,
        invoice_number: str,
        amount: float,
        job_name: str,
        # payment_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Send billing confirmation email to customer
        
        Args:
            customer_email: Customer email address
            customer_name: Customer name
            invoice_number: Invoice number
            amount: Invoice amount
            job_name: Job/project name
            payment_url: Payment URL (optional)
            
        Returns:
            Dict containing success status and details
        """
        try:
            # Email template
            html_template = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Invoice Created - DOTec Engineering</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background-color: #2c3e50; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background-color: #f9f9f9; }
                    .invoice-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
                    .amount { font-size: 24px; font-weight: bold; color: #27ae60; }
                    .button { display: inline-block; padding: 12px 24px; background-color: #3498db; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
                    .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>DOTec Engineering</h1>
                        <h2>Invoice Created Successfully</h2>
                    </div>
                    
                    <div class="content">
                        <p>Dear {{ customer_name }},</p>
                        
                        <p>Your invoice has been created successfully. Here are the details:</p>
                        
                        <div class="invoice-details">
                            <h3>Invoice Details</h3>
                            <p><strong>Invoice Number:</strong> {{ invoice_number }}</p>
                            <p><strong>Project:</strong> {{ job_name }}</p>
                            <p><strong>Amount:</strong> <span class="amount">${{ "%.2f"|format(amount) }}</span></p>
                            <p><strong>Date:</strong> {{ current_date }}</p>
                        </div>
                        
                        {% if payment_url %}
                        <p>You can make a payment using the website below:</p>
                        <a href="https://pay.dotecengineering.com" class="button">Pay Now</a>
                        {% endif %}
                        
                        <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>
                        
                        <p>Thank you for choosing DOTec Engineering!</p>
                    </div>
                    
                    <div class="footer">
                        <p>DOTec Engineering<br>
                        Email: {{ from_email }}<br>
                        This is an automated message, please do not reply.</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Render template
            template = Template(html_template)
            html_content = template.render(
                customer_name=customer_name,
                invoice_number=invoice_number,
                job_name=job_name,
                amount=amount,
                current_date=datetime.now().strftime("%B %d, %Y"),
                from_email=self.from_email
            )
            
            subject = f"Invoice Created: {invoice_number} - DOTec Engineering"
            
            return self.send_email(
                to_emails=[customer_email],
                subject=subject,
                html_content=html_content
            )
            
        except Exception as e:
            error_msg = f"Failed to send billing confirmation email: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg
            }
    def send_invoice_creation_email(
        self,
        customer_email: str,
        customer_name: str,
        amount: float,
        job_name: str,
    ) -> Dict[str, Any]:
        """
        Send billing confirmation email to customer
        
        Args:
            customer_email: Customer email address
            customer_name: Customer name
            invoice_number: Invoice number
            amount: Invoice amount
            job_name: Job/project name
            payment_url: Payment URL (optional)
            
        Returns:
            Dict containing success status and details
        """
        try:
            # Email template
            html_template = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Invoice Created - DOTec Engineering</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background-color: #2c3e50; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background-color: #f9f9f9; }
                    .invoice-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
                    .amount { font-size: 24px; font-weight: bold; color: #27ae60; }
                    .button { display: inline-block; padding: 12px 24px; background-color: #3498db; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
                    .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>DOTec Engineering</h1>
                        <h2>Invoice Created Successfully</h2>
                    </div>
                    
                    <div class="content">
                        <p>Dear {{ customer_name }},</p>
                        
                        <p>Your invoice has been created successfully. Here are the details:</p>
                        
                        <div class="invoice-details">
                            <h3>Invoice Details</h3>
                            <p><strong>Project:</strong> {{ job_name }}</p>
                            <p><strong>Amount:</strong> <span class="amount">${{ "%.2f"|format(amount) }}</span></p>
                            <p><strong>Date:</strong> {{ current_date }}</p>
                        </div>
                  
                        <p>You can see the invoice using the link below:</p>
                        <a href="https://pay.dotecengineering.com" class="button">Open Invoices</a>
                        <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>
                        
                        <p>Thank you for choosing DOTec Engineering!</p>
                    </div>
                    
                    <div class="footer">
                        <p>DOTec Engineering<br>
                        Email: {{ from_email }}<br>
                        This is an automated message, please do not reply.</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Render template
            template = Template(html_template)
            html_content = template.render(
                customer_name=customer_name,
                job_name=job_name,
                amount=amount,
                current_date=datetime.now().strftime("%B %d, %Y"),
                from_email=self.from_email
            )
            
            subject = f"Invoice Created - DOTec Engineering"
            
            return self.send_email(
                to_emails=['<EMAIL>',customer_email],
                subject=subject,
                html_content=html_content
            )
            
        except Exception as e:
            error_msg = f"Failed to send billing confirmation email: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg
            }    
    def send_payment_notification_email(
        self,
        customer_name: str,
        invoice_number: str,
        amount: float,
        job_name: str,
        customer_email: str
    ) -> Dict[str, Any]:
        """
        Send payment notification email to admin
        
        Args:
            customer_name: Customer name
            invoice_number: Invoice number
            amount: Payment amount
            job_name: Job/project name
            customer_email: Customer email address
            
        Returns:
            Dict containing success status and details
        """
        try:
            # Email template for admin notification
            html_template = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Payment Received - DOTec Engineering</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background-color: #27ae60; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background-color: #f9f9f9; }
                    .payment-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
                    .amount { font-size: 24px; font-weight: bold; color: #27ae60; }
                    .highlight { background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
                    .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>💰 Payment Received</h1>
                        <h2>DOTec Engineering</h2>
                    </div>
                    
                    <div class="content">
                        <p>Dear Admin,</p>
                        
                        <p>A payment has been successfully received and processed. Here are the details:</p>
                        
                        <div class="payment-details">
                            <h3>Payment Details</h3>
                            <p><strong>Customer:</strong> {{ customer_name }}</p>
                            <p><strong>Customer Email:</strong> {{ customer_email }}</p>
                            <p><strong>Invoice Number:</strong> {{ invoice_number }}</p>
                            <p><strong>Project:</strong> {{ job_name }}</p>
                            <p><strong>Amount Paid:</strong> <span class="amount">${{ "%.2f"|format(amount) }}</span></p>
                            <p><strong>Date:</strong> {{ current_date }}</p>
                        </div>
                        
                        <div class="highlight">
                            <p><strong>🎉 Cash receipt has been automatically generated in Deltek!</strong></p>
                        </div>
                        
                        <p>The payment has been processed successfully and the job status has been updated accordingly.</p>
                    </div>
                    
                    <div class="footer">
                        <p>DOTec Engineering - Admin Notification<br>
                        This is an automated message from the billing system.</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Render template
            template = Template(html_template)
            html_content = template.render(
                customer_name=customer_name,
                customer_email=customer_email,
                invoice_number=invoice_number,
                job_name=job_name,
                amount=amount,
                current_date=datetime.now().strftime("%B %d, %Y at %I:%M %p")
            )
            
            subject = f"💰 Payment Received: {invoice_number} - ${amount:.2f}"
            
            return self.send_email(
                to_emails=[self.admin_email,"<EMAIL>","<EMAIL>","<EMAIL>"],
                subject=subject,
                html_content=html_content
            )
            
        except Exception as e:
            error_msg = f"Failed to send payment notification email: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg
            }


# Global email service instance
email_service = EmailService()
